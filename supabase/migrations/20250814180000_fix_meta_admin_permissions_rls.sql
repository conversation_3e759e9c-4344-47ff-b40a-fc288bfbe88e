-- Fix infinite recursion in meta_admin_permissions RLS policy
-- The original policy created a circular dependency by querying the same table it was protecting

-- Drop the problematic policy
DROP POLICY IF EXISTS "Super admins can manage meta admin permissions" ON public.meta_admin_permissions;

-- Create corrected policies using SECURITY DEFINER functions to avoid recursion

-- 1. Allow users to view their own meta admin permissions
-- This is needed for the useMetaAdminRole hook to work
CREATE POLICY "Users can view their own meta admin permissions"
ON public.meta_admin_permissions
FOR SELECT
TO authenticated
USING (user_id = auth.uid());

-- 2. Allow super admins to manage all meta admin permissions
-- Uses the SECURITY DEFINER function to avoid circular dependency
CREATE POLICY "Super admins can manage meta admin permissions"
ON public.meta_admin_permissions
FOR ALL
TO authenticated
USING (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'))
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'));

-- 3. Allow super admins to insert new permissions (for initial setup)
-- This ensures the first super admin can be created
CREATE POLICY "Super admins can insert meta admin permissions"
ON public.meta_admin_permissions
FOR INSERT
TO authenticated
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'));

-- Note: The has_meta_admin_role() function is defined with SECURITY DEFINER
-- which means it runs with the privileges of the function owner (postgres)
-- and bypasses RLS policies, preventing the infinite recursion.
